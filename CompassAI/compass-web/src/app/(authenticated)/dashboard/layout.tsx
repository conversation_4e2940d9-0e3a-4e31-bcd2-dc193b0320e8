import React, { type ReactNode } from 'react';

import * as api from '@/services/apis';

import { AICfoOnboardingModal, ConnectRealDataFlow, FreeTrialExpiredModal, Sidebar } from './_components';
import { ProductFruitsProvider } from './providers';

export default async function DashboardLayout({ children }: { children: ReactNode }) {
  const leafCategories = await api.getLeafCategories().catch(() => []);

  return (
    <main className="flex w-full">
      <Sidebar leafCategories={leafCategories} />
      {children}
      <ProductFruitsProvider />
      <ConnectRealDataFlow />
      <FreeTrialExpiredModal />
      <AICfoOnboardingModal />
    </main>
  );
}
