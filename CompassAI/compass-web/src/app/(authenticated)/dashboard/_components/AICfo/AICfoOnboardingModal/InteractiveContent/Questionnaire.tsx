import { useForm } from 'react-hook-form';

import { ControlledCheckbox, TextArea } from '@/ui/components';

import { QUESTIONNAIRE_DATA } from '../constants';
import { useAICfoOnboardingModalContext } from '../context';
import { AICfoOnboardingModalSteps } from '../types';

type QuestionnaireValues = {
  text: string;
  choices: Record<string, boolean>;
};

export default function Questionnaire() {
  const { step } = useAICfoOnboardingModalContext();
  const data =
    step === AICfoOnboardingModalSteps.CHALLENGES_QUESTIONNAIRE
      ? QUESTIONNAIRE_DATA[0]
      : step === AICfoOnboardingModalSteps.METRICS_QUESTIONNAIRE
        ? QUESTIONNAIRE_DATA[1]
        : QUESTIONNAIRE_DATA[2];

  const { register, control } = useForm<QuestionnaireValues>({
    mode: 'onChange',
    defaultValues: {
      text: '',
      choices: data.choices.reduce(
        (acc, choice) => {
          acc[choice] = false;
          return acc;
        },
        {} as Record<string, boolean>,
      ),
    },
  });

  return (
    <div className="flex flex-col gap-4">
      <TextArea
        {...register('text')}
        label={data.question}
        placeholder="Describe your biggest financial challenge..."
        rows={7}
      />
      <div className="flex flex-col gap-3">
        <p className="text-gray-500 body-s-medium">Or choose from common challengess:</p>
        <div className="grid grid-cols-2 gap-6">
          {data.choices.map((choice, index) => (
            <ControlledCheckbox
              key={index}
              name={`choices.${choice}` as keyof QuestionnaireValues}
              control={control}
              label={choice}
              className="body-l-regular"
            />
          ))}
        </div>
      </div>
    </div>
  );
}
