import { createContext, type PropsWithChildren, useContext, useState } from 'react';

import { AICfoOnboardingModalSteps } from './types';

interface AICfoOnboardingModalContextValue {
  step: AICfoOnboardingModalSteps;
  setStep: (newStep: AICfoOnboardingModalSteps) => void;
}

export const AICfoOnboardingModalContext = createContext<AICfoOnboardingModalContextValue>({
  step: AICfoOnboardingModalSteps.INTRODUCTION,
  setStep: () => null,
});

export const AICfoOnboardingModalContextProvider = ({ children }: PropsWithChildren) => {
  const [step, setStep] = useState<AICfoOnboardingModalSteps>(AICfoOnboardingModalSteps.INTRODUCTION);

  return (
    <AICfoOnboardingModalContext.Provider value={{ step, setStep }}>{children}</AICfoOnboardingModalContext.Provider>
  );
};

export const useAICfoOnboardingModalContext = () => {
  const context = useContext(AICfoOnboardingModalContext);

  if (!context) {
    throw new Error('useAICfoOnboardingModalContext must be used within AICfoOnboardingModalContextProvider');
  }

  return context;
};
