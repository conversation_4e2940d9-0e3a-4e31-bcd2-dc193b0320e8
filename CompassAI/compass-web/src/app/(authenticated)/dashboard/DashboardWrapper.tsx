'use client';

import { AICfoOnboardingModalContextProvider } from './_components/AICfo/AICfoOnboardingModal/context';
import { DashboardContextProvider } from './context';
import Dashboard from './Dashboard';
import { ServerSideData } from './types';

interface DashboardWrapperProps {
  serverSideData: ServerSideData;
}

export default function DashboardWrapper({ serverSideData }: DashboardWrapperProps) {
  return (
    <DashboardContextProvider>
      <AICfoOnboardingModalContextProvider>
        <Dashboard serverSideData={serverSideData} />
      </AICfoOnboardingModalContextProvider>
    </DashboardContextProvider>
  );
}
