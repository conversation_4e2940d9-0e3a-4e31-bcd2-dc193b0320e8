# Compass AI API Project Setup

This guide details the setup process for running the Compass AI API project locally, utilizing Python 3.11.7 or greater. The project leverages FastAPI to serve a RESTful API with various endpoints.

## GIT LFS
For purpose of this project you need to install Git LFS to pull some model files (git does not allow storing above 100mb by default). Run `brew install git-lfs` and after `git lfs install`. More about it here: https://git-lfs.com/

## Prerequisites

- Python 3.11.7+: Ensure Python 3.11.7 or newer is installed on your system.
- [pyenv](https://github.com/pyenv/pyenv) or similar tool for managing Python versions.
- PostgreSQL database.
- [PDM](https://pdm.fming.dev/) for dependency management.

Check Python version: `python --version`

## (Optional) Installing pyenv

If you haven't installed pyenv yet, follow the instructions on the pyenv GitHub page (https://github.com/pyenv/pyenv).

After installing pyenv, you can install Python 3.11.7 (if not already installed) and set it as the default version for your project:

```
pyenv install 3.11.7
pyenv local 3.11.7 // or pyenv global 3.11.7
```

## For image processing support

We are using "poppler" to manage pdf2image lib - https://pypi.org/project/pdf2image/

### Mac
Mac users will have to install poppler.

Installing using Brew:
```
brew install poppler
```

### Linux
Most distros ship with `pdftoppm` and `pdftocairo`. If they are not installed, refer to your package manager to install `poppler-utils`

## Installation

Ensure you have [PDM](https://pdm.fming.dev/latest/#installation) installed:

`curl -sSL https://pdm.fming.dev/install-pdm.py | python3 -`

Set Python version for PDM: `pdm use -f 3.11.7`
Install dependencies: `pdm install`

## Setting up Sentence Transformer feature
To use sentence transformer feature, the following needs to be done:
1. install `git-lfs` with `brew install git-lfs` (if not already installed)
2. run `git lfs install` in the root of the project
3. run `git lfs pull` in the root of the project

## (Optional) Setup log.ini for logging
Python logging library is being used for more versatile logging of data. To utilize it, create log.ini file in root and add something like:
```
[loggers]
keys=root

[handlers]
keys=logfile, consolehandler

[formatters]
keys=logfileformatter, consoleformatter

[logger_root]
level=INFO
handlers=logfile, consolehandler

[formatter_logfileformatter]
format=[%(asctime)s.%(msecs)03d] %(levelname)s [%(thread)d] - %(message)s

[formatter_consoleformatter]
format=%(asctime)s %(levelname)s | %(name)s | %(message)s'
datefmt='%d-%m-%Y %H:%M:%S

[handler_logfile]
class=handlers.RotatingFileHandler
level=INFO
args=('logfile.log','a')
formatter=logfileformatter

[handler_consolehandler]
class=StreamHandler
formatter=consoleformatter
args=(sys.stdout,)
```
# Run Redis locally via Docker

```
docker run -d --name redis-stack-server -p 6379:6379 redis/redis-stack-server:latest
```

# Setup database

Create a postgresql database and add path to it in .env.

Run alembic migrations to create tables in database:
```
pdm run alembic upgrade head
```

# Running the Application

Launch the application with Uvicorn, an ASGI server recommended by FastAPI:

```
pdm run uvicorn main:app --reload --port 9000
```

The --reload flag enables auto-reload for development, automatically restarting the server on code changes.

If you want to run customized logging, add `--log-config log.ini` to uvicorn start. Like:

```
pdm run uvicorn main:app --reload --port 9000 --log-config log.ini
```

Accessing API Documentation
FastAPI generates interactive API documentation using Swagger UI, accessible once the application is running:

```
http://127.0.0.1:9000/docs
```

## Environment Variables

For configurations such as database connections or external API endpoints, use environment variables. Define these in a .env file at the project's root.

```
OPEN_AI_API_KEY_ONE=
OPEN_AI_API_KEY_TWO=
AI_API_RESPONSE_TIMEOUT= // defaults to 30
REDIS_URL= // redis://localhost:6379
SERPER_API_KEY=
DATABASE_URL= // posgresql database url like "postgresql://postgres:password@localhost:5433/compassaidb"
COMPASS_API_URL= // http://localhost:8000/
COMPASS_API_KEY= // key for communicating with Compass API
AWS_BEDROCK_REGIONS= // defaults to eu-central-1,us-west-2
```
