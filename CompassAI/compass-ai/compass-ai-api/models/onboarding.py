from db.database import Base
from sqlalchemy import <PERSON><PERSON><PERSON>, Column, DateTime, Foreign<PERSON>ey, Integer, String
from sqlalchemy.sql import func


class OnboardingData(Base):
    __tablename__ = "onboarding_data"

    id = Column(Integer, primary_key=True, index=True)
    company_id = Column(Integer, index=True, unique=True)
    challenges = Column(JSON, default=list, nullable=False)
    kpis = Column(JSON, default=list, nullable=False)
    decisions = Column(JSON, default=list, nullable=False)
    widget_recommendations = Column(JSON, default=list, nullable=False)
    alert_recommendations = Column(JSON, default=list, nullable=False)
    created_at = Column(
        DateTime(timezone=True), server_default=func.now(), nullable=False
    )
    updated_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
    )
