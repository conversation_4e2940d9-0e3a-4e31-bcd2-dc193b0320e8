"""create_onboarding_data_table

Revision ID: a7c9d5e8f2b1
Revises: ab5a7dd5ba16
Create Date: 2025-06-10 14:30:00.000000

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op
from sqlalchemy.sql import func

# revision identifiers, used by Alembic.
revision: str = "a7c9d5e8f2b1"
down_revision: Union[str, None] = "f8a7b9c2d3e4"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "onboarding_data",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("company_id", sa.Integer(), nullable=False),
        sa.Column("challenges", sa.JSON(), server_default="[]", nullable=False),
        sa.Column("kpis", sa.<PERSON>(), server_default="[]", nullable=False),
        sa.<PERSON>umn("decisions", sa.JSON(), server_default="[]", nullable=False),
        sa.Column(
            "widget_recommendations", sa.JSON(), server_default="[]", nullable=False
        ),
        sa.Column(
            "alert_recommendations", sa.JSON(), server_default="[]", nullable=False
        ),
        sa.Column(
            "created_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column(
            "updated_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_onboarding_data_company_id"),
        "onboarding_data",
        ["company_id"],
        unique=True,
    )
    op.create_index(
        op.f("ix_onboarding_data_id"), "onboarding_data", ["id"], unique=False
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_onboarding_data_id"), table_name="onboarding_data")
    op.drop_index(op.f("ix_onboarding_data_company_id"), table_name="onboarding_data")
    op.drop_table("onboarding_data")
    # ### end Alembic commands ###
