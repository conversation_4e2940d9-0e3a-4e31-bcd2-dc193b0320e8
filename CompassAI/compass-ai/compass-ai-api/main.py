import logging

from controllers.alert.router import router as alert_router
from controllers.assistant.router import router as assistant_router
from controllers.cfo.onboarding.router import router as cfo_onboarding_router
from controllers.contexts.router import router as contexts_router
from controllers.forecasts.router import router as forecasts_router
from controllers.insights.router import router as insights_router
from controllers.invoices.router import router as invoices_router
from controllers.scenarios.router import router as scenarios_router
from controllers.similarity.router import router as similarity_router
from controllers.status.router import router as status_router
from controllers.subscriptions.router import router as subscriptions_router
from controllers.transaction.router import router as transaction_router
from controllers.widget.router import router as widget_router
from dotenv import load_dotenv
from fastapi import FastAPI, Request, status
from fastapi.exceptions import RequestValidationError
from fastapi.responses import JSONResponse
from utils.text_similarity import load_mpNet_model

load_dotenv()

app = FastAPI()

app.include_router(status_router)
app.include_router(transaction_router)
app.include_router(widget_router)
app.include_router(alert_router)
app.include_router(similarity_router)
app.include_router(insights_router)
app.include_router(invoices_router)
app.include_router(subscriptions_router)
app.include_router(assistant_router)
app.include_router(contexts_router)
app.include_router(forecasts_router)
app.include_router(scenarios_router)
app.include_router(cfo_onboarding_router)


@app.on_event("startup")
async def on_startup():
    load_mpNet_model()


@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request: Request, exc: RequestValidationError):
    exc_str = f"{exc}".replace("\n", " ").replace("   ", " ")
    logging.error(f"{request}: {exc_str}")
    content = {"status_code": 10422, "message": exc_str, "data": None}
    return JSONResponse(
        content=content, status_code=status.HTTP_422_UNPROCESSABLE_ENTITY
    )
