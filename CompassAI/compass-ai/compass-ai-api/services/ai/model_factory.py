import itertools
import logging

import boto3
from botocore.config import Config
from langchain_aws import ChatBedrock
from langchain_openai import ChatOpenAI

from .config.constants import AIProvider
from .config.settings import settings


class ModelFactory:
    _region_cycle = None

    @staticmethod
    def create_model(provider: AIProvider, model_name: str):
        if provider == AIProvider.BEDROCK_ONE:
            return ModelFactory._create_bedrock_model(model_name)
        elif provider == AIProvider.OPEN_AI_ONE:
            return ModelFactory._create_openai_model(model_name)
        raise ValueError(f"Unsupported AI provider: {provider}")

    @staticmethod
    def _get_next_region():
        if ModelFactory._region_cycle is None:
            ModelFactory._region_cycle = itertools.cycle(settings.AWS_BEDROCK_REGIONS)
        return next(ModelFactory._region_cycle)

    @staticmethod
    def _create_bedrock_model(model_name: str):
        region = ModelFactory._get_next_region()
        logging.info(f"\033[91mCreating Bedrock model in region: {region}\033[0m")
        bedrock_client = boto3.client(
            service_name="bedrock-runtime",
            region_name=region,
            config=Config(max_pool_connections=50, retries={"max_attempts": 3}),
        )

        return ChatBedrock(
            client=bedrock_client,
            model_id=model_name,
            model_kwargs=dict(
                temperature=settings.DEFAULT_TEMPERATURE,
                max_tokens=settings.DEFAULT_MAX_TOKENS,
            ),
        )

    @staticmethod
    def _create_openai_model(model_name: str):
        return ChatOpenAI(
            api_key=settings.OPEN_AI_API_KEY_ONE,
            temperature=settings.DEFAULT_TEMPERATURE,
            max_tokens=settings.DEFAULT_MAX_TOKENS,
            model=model_name,
        )
