import asyncio
import logging
from abc import ABC, abstractmethod

from fastapi import <PERSON><PERSON><PERSON>Ex<PERSON>
from services.ai.config.constants import MODEL_NAME_MAPPER, AIProvider
from services.ai.config.settings import settings
from services.ai.model_factory import ModelFactory


class BaseAIStrategy(ABC):
    def __init__(self, use_case: str):
        self.use_case = use_case
        self.models = MODEL_NAME_MAPPER.get(use_case, MODEL_NAME_MAPPER["default"])

    @abstractmethod
    async def execute(self, *args, **kwargs):
        pass

    @abstractmethod
    def _create_chain(self, model, *args, **kwargs):
        pass

    def _get_model(self, endpoint: AIProvider):
        try:
            model_name = self.models[endpoint]
            return ModelFactory.create_model(endpoint, model_name)
        except Exception as e:
            logging.error(f"Failed to create model for endpoint {endpoint}: {e}")
            raise

    async def _handle_request(
        self, endpoint: AIProvider, chain_args: tuple, input_data: dict
    ):
        try:
            async with asyncio.timeout(settings.AI_API_RESPONSE_TIMEOUT):
                model = self._get_model(endpoint)
                logging.info(
                    f"Model: {model}, Region: {getattr(model, 'region', 'unknown')}"
                )

                chain = self._create_chain(model, *chain_args)
                logging.info(f"Executing chain with endpoint {endpoint}")
                return await chain.ainvoke(input_data)
        except Exception as e:
            logging.error(
                f"Primary request failed with model: {model}, in region: {getattr(model, 'region', 'unknown')}"
            )

            logging.error(f"Primary request failed with {endpoint}: {e}", exc_info=True)
            return await self._handle_fallback(chain_args, input_data)

    async def _handle_fallback(self, chain_args: tuple, input_data: dict):
        try:
            logging.info("Attempting fallback to OpenAI...")
            model = self._get_model(AIProvider.OPEN_AI_ONE)
            chain = self._create_chain(model, *chain_args)
            return await chain.ainvoke(input_data)
        except Exception as e:
            logging.error(f"Fallback request failed: {e}")
            raise HTTPException(
                status_code=504, detail="All AI providers failed to respond"
            )
