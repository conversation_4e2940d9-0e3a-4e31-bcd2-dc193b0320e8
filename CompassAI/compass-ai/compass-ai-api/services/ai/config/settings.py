import os
from functools import lru_cache

from pydantic import BaseModel


class Settings(BaseModel):
    AI_API_RESPONSE_TIMEOUT: int = int(os.getenv("AI_API_RESPONSE_TIMEOUT", 60))
    OPEN_AI_API_KEY_ONE: str = os.getenv("OPEN_AI_API_KEY_ONE")
    AWS_BEDROCK_REGIONS: list[str] = os.getenv(
        "AWS_BEDROCK_REGIONS", "eu-central-1,us-east-1,us-east-2,us-west-2"
    ).split(",")

    # Model configurations
    DEFAULT_MAX_TOKENS: int = 8192
    DEFAULT_TEMPERATURE: float = 0.0

    class Config:
        env_file = ".env"
        case_sensitive = True


@lru_cache(maxsize=None)
def get_settings():
    return Settings()


settings = get_settings()
