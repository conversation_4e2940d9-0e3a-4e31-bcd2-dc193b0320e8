import logging
from typing import AsyncGenerator, List

from services.ai.service import AIGatewayService

from ..schemas import KPIInvestigationResponse, OnboardingResponse, OnboardingState
from .base import WorkflowState


class KPIInvestigationState(WorkflowState):
    """
    State for investigating the key performance indicators a company is tracking.
    """

    async def handle_stream(self, context) -> AsyncGenerator[OnboardingResponse, None]:
        logging.info(
            f"Processing KPI investigation for company: {context.company.name}"
        )

        # Process user answers to generate refined KPIs
        response = await self._process_kpis(context)

        # Update context with refined KPIs
        context.kpis = response.kpis

        # Save to database
        await self.save_to_database(context)

        # Create and yield response
        yield OnboardingResponse(
            current_state=OnboardingState.KPI_INVESTIGATION,
            text_response="Here are the key performance indicators I recommend tracking.",
            data_response=response.kpis,
        )

    async def _process_kpis(self, context) -> KPIInvestigationResponse:
        """Process user answers to generate refined KPIs"""
        system_prompt = self._build_system_prompt(context)
        user_prompt = self._build_user_prompt(context)

        response = await AIGatewayService.handle_single_prompt(
            system_prompt, user_prompt, KPIInvestigationResponse
        )

        return response

    def _build_system_prompt(self, context) -> str:
        return f"""You are an expert financial advisor helping a CFO identify the most important KPIs for their company.

Based on the user's answers and their previously identified challenges, generate a refined list of 3-5 specific financial KPIs that this company should be tracking.
Focus on metrics related to financial health, operational efficiency, and strategic growth.

The company is in the {context.company.industry} industry and is called {context.company.name}.
Their key challenges include: {', '.join(context.challenges)}

Your response should be a JSON object with two fields:
1. "kpis": An array of strings, each describing a specific financial KPI
2. "reasoning": A brief explanation of why you selected these KPIs based on the user's input and challenges

Be specific and actionable in your recommendations."""

    def _build_user_prompt(self, context) -> str:
        answers = "\n".join([f"- {answer}" for answer in context.answers])
        return f"""Here are the answers the CFO provided about the KPIs they currently track:

{answers}

Based on these answers and their key challenges, what are the most important financial KPIs this company should be tracking?"""
