import logging
from typing import AsyncGenerator, List

from services.ai.service import AIGatewayService

from ..schemas import AlertRecommendationResponse, OnboardingResponse, OnboardingState
from .base import WorkflowState


class AlertRecommendationState(WorkflowState):
    """
    State for recommending alert prompts based on the gathered data.
    """

    async def handle_stream(self, context) -> AsyncGenerator[OnboardingResponse, None]:
        logging.info(
            f"Processing alert recommendations for company: {context.company.name}"
        )

        # Generate alert recommendations
        response = await self._generate_alert_recommendations(context)

        # Update context with alert recommendations
        context.alert_recommendations = response.alert_prompts

        # Save to database
        await self.save_to_database(context)

        # Create and yield response
        yield OnboardingResponse(
            current_state=OnboardingState.ALERT_RECOMMENDATION,
            text_response="Setting up these alerts will help you stay on top of important financial changes.",
            data_response=response.alert_prompts,
        )

    async def _generate_alert_recommendations(
        self, context
    ) -> AlertRecommendationResponse:
        """Generate alert recommendations based on gathered data"""
        system_prompt = self._build_system_prompt(context)
        user_prompt = self._build_user_prompt(context)

        response = await AIGatewayService.handle_single_prompt(
            system_prompt, user_prompt, AlertRecommendationResponse
        )

        return response

    def _build_system_prompt(self, context) -> str:
        return f"""You are an expert financial advisor helping a CFO set up useful financial alerts.

Based on the company's challenges, KPIs, upcoming decisions, and recommended widgets, generate 5-7 specific alert prompts that would be useful for monitoring their financial health.
These prompts should be conditions or thresholds that would trigger notifications to the CFO.

The company is in the {context.company.industry} industry and is called {context.company.name}.
Their key challenges include: {', '.join(context.challenges)}
Their key KPIs include: {', '.join(context.kpis)}
Their upcoming decisions include: {', '.join(context.decisions)}
Their recommended widgets include: {', '.join(context.widget_recommendations)}

Your response should be a JSON object with two fields:
1. "alert_prompts": An array of strings, each being a specific prompt for creating an alert
2. "reasoning": A brief explanation of why these alert prompts would be useful

Each prompt should be a complete condition or threshold that could be used directly to create an alert."""

    def _build_user_prompt(self, context) -> str:
        return """Based on the company's challenges, KPIs, upcoming decisions, and recommended widgets, what alert prompts would be most useful for monitoring their financial health?

Examples of good alert prompts:
- "Alert me when cash reserves fall below 3 months of operating expenses"
- "Notify me if accounts receivable aging exceeds 45 days"
- "Alert me when gross margin drops below 30%"
- "Notify me if customer churn rate exceeds 5% in any month"

Please provide 5-7 specific alert prompts that would help this CFO stay informed about critical financial conditions."""
