import logging
from typing import AsyncGenerator, List

from services.ai.service import AIGatewayService
from services.ai.utils.logging_decorator import log_ai_interaction

from ..schemas import (
    ChallengeInvestigationResponse,
    ChallengeStreamItem,
    OnboardingResponse,
    OnboardingState,
)
from .base import WorkflowState


class ChallengeInvestigationState(WorkflowState):
    """
    State for investigating the biggest challenges a company is facing.
    """

    async def handle_stream(self, context) -> AsyncGenerator[OnboardingResponse, None]:
        if context.request.intro_interaction:
            intro_messages = self._generate_intro_messages(context)
            for message in intro_messages:
                yield OnboardingResponse(
                    current_state=OnboardingState.CHALLENGE_INVESTIGATION,
                    text_response=message,
                    data_response=[],
                )

            return
        else:
            logging.info(
                f"Processing challenge investigation for company: {context.company.name}"
            )

            # Stream the processing
            async for response in self._process_challenges_streaming(context):
                yield response
            return

    async def _process_challenges(self, context) -> tuple[List[str], str]:
        """Process user answers to generate refined challenges"""
        system_prompt = self._build_system_prompt(context)
        user_prompt = self._build_user_prompt(context)

        response = await AIGatewayService.handle_single_prompt(
            system_prompt, user_prompt, ChallengeInvestigationResponse
        )

        return response

    def _build_system_prompt(self, context) -> str:
        return f"""
            You are an expert financial advisor, an AI CFO so to speak, helping a person in the financial
            department of a company or a founder identify their company's biggest challenges.

            Based on the user's answers, generate a refined list of 3-5 specific financial challenges that
            this company is likely facing. Focus on the challenges provided in the answer, work on refining them into more specific,
            finer granulated options, and finding common themes in the challenges provided.

            Stream your response as NDJSON with two types of items:
            1. Response chunks: {{"item_type": "response_chunk", "response_chunk": "partial text"}}
            2. Challenge items: {{"item_type": "challenge_item", "challenge_item": "specific challenge"}}

            First stream response chunks building up your analysis.
            Then stream 3-5 challenge items as separate lines.

            COMPANY INFO:
            * company name: {context.company.name}
            * industry: {context.company.industry}
            * country: {context.company.country}

        """

    def _build_user_prompt(self, context) -> str:
        answers = "\n".join([f"- {answer}" for answer in context.answers])
        return f"""
            Here are the answers the user provided about their company's challenges:

            {answers}

            Based on these answers, what are the most important financial challenges this company is facing? What conclusions can we make when we see them all together?
        """

    async def _process_challenges_streaming(
        self, context
    ) -> AsyncGenerator[OnboardingResponse, None]:
        """Process user answers with streaming response"""
        import time

        start_time = time.time()
        first_chunk_time = None

        system_prompt = self._build_streaming_system_prompt(context)
        user_prompt = self._build_user_prompt(context)

        accumulated_response = ""
        challenges = []

        async for chunk in AIGatewayService.handle_ndjson_streaming(
            system_prompt, user_prompt, ChallengeStreamItem
        ):
            if chunk.get("status") == "line_complete" and chunk.get("data"):
                stream_item = ChallengeStreamItem(**chunk["data"])

                if (
                    stream_item.item_type == "response_chunk"
                    and stream_item.response_chunk
                ):
                    if first_chunk_time is None:
                        first_chunk_time = time.time()
                        time_to_first_chunk = first_chunk_time - start_time
                        logging.info(
                            f"Time to first chunk: {time_to_first_chunk:.2f} seconds"
                        )

                    accumulated_response += stream_item.response_chunk
                    yield OnboardingResponse(
                        current_state=OnboardingState.CHALLENGE_INVESTIGATION,
                        text_response=stream_item.response_chunk,
                        data_response=[],
                    )

                elif (
                    stream_item.item_type == "challenge_item"
                    and stream_item.challenge_item
                ):
                    challenges.append(stream_item.challenge_item)
                    # yield OnboardingResponse(
                    #    current_state=OnboardingState.CHALLENGE_INVESTIGATION,
                    #    text_response=accumulated_response,
                    #    data_response=challenges,
                    # )

        # Final response with complete data
        context.challenges = challenges
        await self.save_to_database(context)

    def _build_streaming_system_prompt(self, context) -> str:
        return f"""
            You are an expert financial advisor, an AI CFO so to speak, helping a person in the financial
            department of a company or a founder identify their company's biggest challenges.

            Based on the user's answers, generate a refined list of 3-5 specific financial challenges that
            this company is likely facing. Focus on the challenges provided in the answer, work on refining them into more specific,
            finer granulated options, and finding common themes in the challenges provided.

            COMPANY INFO:
            * company name: {context.company.name}
            * industry: {context.company.industry}
            * country: {context.company.country}

            Your response should be a JSON object with two fields:
            1. "response": A brief reply to the user's input, attempting to find common themes in the challenges provided, and acknowledging that you will take them into account in the next steps. Keep the response under 50 words
            2. "challenges": An array of strings, each describing a specific financial challenge

            Be specific and actionable in your recommendations.
        """

    def _generate_intro_messages(self, context) -> List[str]:
        """Generate intro messages based on user role and company"""
        user_role = context.request.user_role

        if user_role in ["Founder", "Other", "Operations Manager"]:
            return [
                "Every business faces unique challenges, and yours is no different.",
                "I've seen companies with strong revenue struggle with cash flow timing. Others with healthy margins hit unexpected expense spikes. Some founders know exactly what keeps them up at night, while others feel a general unease but can't pinpoint the source.",
                "Help me understand what's weighing on you right now so I can focus on the areas that matter most.",
            ]
        elif user_role in ["CFO", "Accountant", "Financial Advisor"]:
            return [
                "Every finance function has different analytical priorities and reporting requirements",
                "Help me understand your biggest analytical challenge so I can prioritize automation in the areas that will save you the most time. Once integrated with your systems, I'll handle the heavy lifting on these analyses while you focus on interpretation and strategic recommendations.",
            ]
        else:
            raise ValueError(f"Unsupported user role: {user_role}")
