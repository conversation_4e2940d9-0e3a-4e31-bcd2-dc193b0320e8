from abc import ABC, abstractmethod
from typing import AsyncGenerator

from ..schemas import OnboardingResponse


class WorkflowState(ABC):
    """Base class for all onboarding workflow states"""

    @abstractmethod
    async def handle_stream(self, context) -> AsyncGenerator[OnboardingResponse, None]:
        """Handle the current state and yield responses"""
        pass

    async def save_to_database(self, context):
        """Save the current state of the onboarding process to the database"""
        from ..db_service import OnboardingDBService

        await OnboardingDBService.save_onboarding_data(
            context.db,
            context.company.id,
            challenges=context.challenges,
            kpis=context.kpis,
            decisions=context.decisions,
            widget_recommendations=context.widget_recommendations,
            alert_recommendations=context.alert_recommendations,
        )
