import logging
from typing import AsyncGenerator, List

from services.ai.service import AIGatewayService

from ..schemas import OnboardingResponse, OnboardingState, WidgetRecommendationResponse
from .base import WorkflowState


class WidgetRecommendationState(WorkflowState):
    """
    State for recommending widget prompts based on the gathered data.
    """

    async def handle_stream(self, context) -> AsyncGenerator[OnboardingResponse, None]:
        logging.info(
            f"Processing widget recommendations for company: {context.company.name}"
        )

        # Generate widget recommendations
        response = await self._generate_widget_recommendations(context)

        # Update context with widget recommendations
        context.widget_recommendations = response.widget_prompts

        # Save to database
        await self.save_to_database(context)

        # Create and yield response
        yield OnboardingResponse(
            current_state=OnboardingState.WIDGET_RECOMMENDATION,
            text_response="I recommend adding these widgets to your dashboard.",
            data_response=response.widget_prompts,
        )

    async def _generate_widget_recommendations(
        self, context
    ) -> WidgetRecommendationResponse:
        """Generate widget recommendations based on gathered data"""
        system_prompt = self._build_system_prompt(context)
        user_prompt = self._build_user_prompt(context)

        response = await AIGatewayService.handle_single_prompt(
            system_prompt, user_prompt, WidgetRecommendationResponse
        )

        return response

    def _build_system_prompt(self, context) -> str:
        return f"""You are an expert financial advisor helping a CFO create useful dashboard widgets.

Based on the company's challenges, KPIs, and upcoming decisions, generate 5-7 specific widget prompts that would be useful for their financial dashboard.
These prompts should be questions or requests that could be used to create charts, graphs, or other visualizations.

The company is in the {context.company.industry} industry and is called {context.company.name}.
Their key challenges include: {', '.join(context.challenges)}
Their key KPIs include: {', '.join(context.kpis)}
Their upcoming decisions include: {', '.join(context.decisions)}

Your response should be a JSON object with two fields:
1. "widget_prompts": An array of strings, each being a specific prompt for creating a widget
2. "reasoning": A brief explanation of why these widget prompts would be useful

Each prompt should be a complete question or request that could be used directly to create a visualization."""

    def _build_user_prompt(self, context) -> str:
        return """Based on the company's challenges, KPIs, and upcoming decisions, what widget prompts would be most useful for their financial dashboard?

Examples of good widget prompts:
- "Show me a cash flow forecast for the next 12 months"
- "Create a breakdown of our top 5 expense categories"
- "Display our customer acquisition cost trend over the past year"
- "Show our current ratio compared to industry benchmarks"

Please provide 5-7 specific widget prompts that would help this CFO monitor their financial performance and make better decisions."""
