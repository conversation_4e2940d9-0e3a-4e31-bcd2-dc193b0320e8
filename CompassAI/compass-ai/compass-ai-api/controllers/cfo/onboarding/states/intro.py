import logging
from typing import Async<PERSON>enerator, List

from ..schemas import OnboardingResponse, OnboardingState
from .base import WorkflowState


class IntroState(WorkflowState):
    """
    Intro state for providing initial messages to the user at the start of onboarding.
    """

    async def handle_stream(self, context) -> AsyncGenerator[OnboardingResponse, None]:
        logging.info(
            f"Processing intro for user: {context.request.user_name}, role: {context.request.user_role}"
        )
        intro_messages = self._generate_intro_messages(context)

        for message in intro_messages:
            yield OnboardingResponse(
                current_state=OnboardingState.INTRO,
                text_response=message,
                data_response=[],
            )

    def _generate_intro_messages(self, context) -> List[str]:
        """Generate intro messages based on user role and company"""
        user_name = context.request.user_name
        user_role = context.request.user_role

        if user_role in ["Founder", "Other", "Operations Manager"]:
            return [
                f"Hi {user_name}, I’m <PERSON><PERSON>, your AI CFO.",
                "I've worked with many founders like you, and I know the understand the anxiety that come with financial uncertainty. My job is to give you the clarity and confidence you need to make smart decisions.",
                "I monitor your cash flow 24/7, catch problems before they become crises, and surface opportunities you might miss. This initial setup helps me understand your priorities, but the real value comes when you connect your data, that's when I can provide deep financial analysis, strategic recommendations, and truly personalized guidance.",
            ]
        elif user_role in ["CFO", "Accountant", "Financial Advisor"]:
            return [
                f"Hi {user_name}, I’m Nika, your AI CFO.",
                "I've worked alongside finance experts, and I understand the time you spend on manual analysis, variance reporting, and board deck preparation. My job is to automate the tedious work so you can focus on strategic analysis and decision support.",
                "I can handle continuous monitoring, predictive modeling, and exception reporting while you drive strategic initiatives. This initial setup helps me understand your analytical priorities, but the real efficiency gains come when I'm integrated with your full data stack, that's when I can automate complex workflows, generate board-ready analytics, and provide sophisticated forecasting models.",
            ]
        else:
            raise ValueError(f"Unsupported user role: {user_role}")
