import logging
from typing import AsyncGenerator, List

from services.ai.service import AIGatewayService

from ..schemas import DecisionInvestigationResponse, OnboardingResponse, OnboardingState
from .base import WorkflowState


class DecisionInvestigationState(WorkflowState):
    """
    State for investigating the upcoming financial decisions a company is facing.
    """

    async def handle_stream(self, context) -> AsyncGenerator[OnboardingResponse, None]:
        logging.info(
            f"Processing decision investigation for company: {context.company.name}"
        )

        # Process user answers to generate refined decisions
        response = await self._process_decisions(context)

        # Update context with refined decisions
        context.decisions = response.decisions

        # Save to database
        await self.save_to_database(context)

        # Create and yield response
        yield OnboardingResponse(
            current_state=OnboardingState.DECISION_INVESTIGATION,
            text_response="These are important financial decisions you may need to make soon.",
            data_response=response.decisions,
        )

    async def _process_decisions(self, context) -> DecisionInvestigationResponse:
        """Process user answers to generate refined decisions"""
        system_prompt = self._build_system_prompt(context)
        user_prompt = self._build_user_prompt(context)

        response = await AIGatewayService.handle_single_prompt(
            system_prompt, user_prompt, DecisionInvestigationResponse
        )

        return response

    def _build_system_prompt(self, context) -> str:
        return f"""You are an expert financial advisor helping a CFO identify important upcoming financial decisions.

Based on the user's answers, their challenges, and their KPIs, generate a refined list of 3-5 specific financial decisions that this company will likely need to make in the near future.
Focus on decisions related to investment, cost management, financing, and strategic planning.

The company is in the {context.company.industry} industry and is called {context.company.name}.
Their key challenges include: {', '.join(context.challenges)}
Their key KPIs include: {', '.join(context.kpis)}

Your response should be a JSON object with two fields:
1. "decisions": An array of strings, each describing a specific financial decision
2. "reasoning": A brief explanation of why you selected these decisions based on the user's input, challenges, and KPIs

Be specific and actionable in your recommendations."""

    def _build_user_prompt(self, context) -> str:
        answers = "\n".join([f"- {answer}" for answer in context.answers])
        return f"""Here are the answers the CFO provided about upcoming financial decisions:

{answers}

Based on these answers, their key challenges, and their KPIs, what are the most important financial decisions this company will need to make in the near future?"""
