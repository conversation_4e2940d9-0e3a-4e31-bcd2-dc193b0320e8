import asyncio
import json
from typing import Any, Async<PERSON>enerator, Dict

from services.ai.token_tracker import TokenTracker
from sqlalchemy.orm import Session

from .context import OnboardingWorkflowContext
from .schemas import OnboardingInputRequest, OnboardingState
from .states import (
    AlertRecommendationState,
    ChallengeInvestigationState,
    DecisionInvestigationState,
    IntroState,
    KPIInvestigationState,
    WidgetRecommendationState,
)


class OnboardingWorkflowService:

    @staticmethod
    async def process_onboarding_stream(
        request: OnboardingInputRequest, db: Session
    ) -> AsyncGenerator[str, None]:
        try:
            # Create context from request
            context = OnboardingWorkflowContext(request, db)

            print(context)
            # Set initial state based on request step
            if request.step == OnboardingState.INTRO:
                context.current_state = IntroState()
            elif request.step == OnboardingState.CHALLENGE_INVESTIGATION:
                context.current_state = ChallengeInvestigationState()
            elif request.step == OnboardingState.KPI_INVESTIGATION:
                context.current_state = KPIInvestigationState()
            elif request.step == OnboardingState.DECISION_INVESTIGATION:
                context.current_state = DecisionInvestigationState()
            elif request.step == OnboardingState.WIDGET_RECOMMENDATION:
                context.current_state = WidgetRecommendationState()
            elif request.step == OnboardingState.ALERT_RECOMMENDATION:
                context.current_state = AlertRecommendationState()

            # Process the workflow
            async for workflow_response in context.process_input_stream():
                response_dict = workflow_response.model_dump()

                yield json.dumps(response_dict, default=str) + "\n"
                await asyncio.sleep(0.01)

        except Exception as e:
            import traceback

            traceback.print_exc()
            yield json.dumps(
                {"error": str(e)},
                default=str,
            ) + "\n"
        finally:
            db.close()
