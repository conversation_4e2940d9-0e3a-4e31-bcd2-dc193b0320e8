import json

from db.database import get_db
from fastapi import APIRouter, Depends
from fastapi.responses import StreamingResponse
from services.ai.token_tracker import TokenTracker
from services.api.dependencies import get_token_tracker
from sqlalchemy.orm import Session

from .schemas import OnboardingInputRequest
from .service import OnboardingWorkflowService

router = APIRouter()


@router.post("/cfo/onboarding/workflow/stream")
async def onboarding_workflow_stream(
    request: OnboardingInputRequest,
    db: Session = Depends(get_db),
):
    print(request)
    output = OnboardingWorkflowService.process_onboarding_stream(request, db)
    return StreamingResponse(
        output,
        media_type="application/x-ndjson",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Transfer-Encoding": "chunked",
        },
    )
