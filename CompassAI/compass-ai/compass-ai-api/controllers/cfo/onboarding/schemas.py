from enum import Enum
from typing import List, Optional

from models import Company
from pydantic import BaseModel, Field


class OnboardingState(str, Enum):
    INTRO = "INTRO"
    CHALLENGE_INVESTIGATION = "CHALLENGE_INVESTIGATION"
    KPI_INVESTIGATION = "KPI_INVESTIGATION"
    DECISION_INVESTIGATION = "DECISION_INVESTIGATION"
    WIDGET_RECOMMENDATION = "WIDGET_RECOMMENDATION"
    ALERT_RECOMMENDATION = "ALERT_RECOMMENDATION"


class OnboardingInputRequest(BaseModel):
    company: Company
    answers: Optional[List[str]] = Field(
        None, description="List of user's answers from the current onboarding view"
    )
    step: OnboardingState = Field(
        None,
        description="The state that correlates to which onboarding view is currenlty being interacted with",
    )
    user_name: str = Field(
        ..., description="Name of the user interacting with the onboarding flow"
    )
    user_role: str = Field(
        ..., description="Role of the user interacting with the onboarding flow"
    )
    intro_interaction: bool = Field(
        False,
        description="Whether the request relates to the intro messages of the view being interacted with",
    )


class OnboardingResponse(BaseModel):
    current_state: OnboardingState
    text_response: str = Field("", description="Text response to display to the user")
    data_response: List[str] = Field(
        default_factory=list,
        description="Structured data response for the current state",
    )


class ChallengeInvestigationResponse(BaseModel):
    response: str = Field(..., description="Response to the user's input")
    challenges: List[str] = Field(
        ..., description="List of identified business challenges"
    )


class KPIInvestigationResponse(BaseModel):
    kpis: List[str] = Field(..., description="List of recommended KPIs to track")
    reasoning: str = Field(
        ..., description="Explanation of why these KPIs were selected"
    )


class DecisionInvestigationResponse(BaseModel):
    decisions: List[str] = Field(
        ..., description="List of important upcoming financial decisions"
    )
    reasoning: str = Field(
        ..., description="Explanation of why these decisions are important"
    )


class WidgetRecommendationResponse(BaseModel):
    widget_prompts: List[str] = Field(
        ..., description="List of recommended dashboard widgets"
    )
    reasoning: str = Field(
        ..., description="Explanation of why these widgets were recommended"
    )


class AlertRecommendationResponse(BaseModel):
    alert_prompts: List[str] = Field(
        ..., description="List of recommended financial alerts"
    )
    reasoning: str = Field(
        ..., description="Explanation of why these alerts were recommended"
    )


class ChallengeStreamItem(BaseModel):
    item_type: str = Field(description="Type: 'response_chunk' or 'challenge_item'")
    response_chunk: Optional[str] = None
    challenge_item: Optional[str] = None

    @classmethod
    def create_response_chunk(cls, chunk: str):
        return cls(item_type="response_chunk", response_chunk=chunk)

    @classmethod
    def create_challenge_item(cls, challenge: str):
        return cls(item_type="challenge_item", challenge_item=challenge)
