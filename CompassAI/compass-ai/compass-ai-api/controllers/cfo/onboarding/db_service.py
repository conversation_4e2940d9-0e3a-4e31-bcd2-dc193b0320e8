from datetime import datetime
from typing import Any, Dict, List, Optional

from models.onboarding import OnboardingData
from sqlalchemy import and_
from sqlalchemy.orm import Session


class OnboardingDBService:
    """Database service for onboarding workflow"""

    @staticmethod
    def get_onboarding_data(db: Session, company_id: int) -> Optional[OnboardingData]:
        """Get onboarding data for a company"""
        return (
            db.query(OnboardingData)
            .filter(OnboardingData.company_id == company_id)
            .first()
        )

    @staticmethod
    async def save_onboarding_data(
        db: Session,
        company_id: int,
        challenges: List[str] = None,
        kpis: List[str] = None,
        decisions: List[str] = None,
        widget_recommendations: List[str] = None,
        alert_recommendations: List[str] = None,
    ) -> OnboardingData:
        """Save onboarding data for a company"""
        # Check if data already exists
        onboarding_data = OnboardingDBService.get_onboarding_data(db, company_id)

        if onboarding_data:
            # Update existing data
            if challenges is not None:
                onboarding_data.challenges = challenges
            if kpis is not None:
                onboarding_data.kpis = kpis
            if decisions is not None:
                onboarding_data.decisions = decisions
            if widget_recommendations is not None:
                onboarding_data.widget_recommendations = widget_recommendations
            if alert_recommendations is not None:
                onboarding_data.alert_recommendations = alert_recommendations

            # Explicitly update the updated_at timestamp
            onboarding_data.updated_at = datetime.now()
        else:
            # Create new data
            onboarding_data = OnboardingData(
                company_id=company_id,
                challenges=challenges or [],
                kpis=kpis or [],
                decisions=decisions or [],
                widget_recommendations=widget_recommendations or [],
                alert_recommendations=alert_recommendations or [],
            )
            db.add(onboarding_data)

        db.commit()
        db.refresh(onboarding_data)
        return onboarding_data
