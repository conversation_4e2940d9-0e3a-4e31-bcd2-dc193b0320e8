from typing import AsyncGenerator, List, Optional

from sqlalchemy.orm import Session

from .db_service import OnboardingDBService
from .schemas import OnboardingInputRequest, OnboardingResponse, OnboardingState


class OnboardingWorkflowContext:
    """Context for the onboarding workflow state machine"""

    def __init__(self, request: OnboardingInputRequest, db: Session):
        self.request = request
        self.db = db
        self.company = request.company
        self.answers = request.answers
        self.current_state = None
        self.challenges = []
        self.kpis = []
        self.decisions = []
        self.widget_recommendations = []
        self.alert_recommendations = []

        # Load existing data if available
        self._load_existing_data()

    def _load_existing_data(self):
        """Load existing onboarding data for this company"""
        onboarding_data = OnboardingDBService.get_onboarding_data(
            self.db, self.company.id
        )

        if onboarding_data:
            self.challenges = onboarding_data.challenges or []
            self.kpis = onboarding_data.kpis or []
            self.decisions = onboarding_data.decisions or []
            self.widget_recommendations = onboarding_data.widget_recommendations or []
            self.alert_recommendations = onboarding_data.alert_recommendations or []

    def transition_to(self, state):
        """Transition to a new state"""
        self.current_state = state

    async def process_input_stream(self) -> AsyncGenerator[OnboardingResponse, None]:
        """Process the current state and yield responses"""
        async for response in self.current_state.handle_stream(self):
            yield response
