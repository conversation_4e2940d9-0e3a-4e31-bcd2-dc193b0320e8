import {
  aws<PERSON><PERSON><PERSON>,
  AwsRegion,
  BadRequestError,
  ConflictError,
  ForbiddenError,
  InternalServerError,
  juneService,
  logger,
  mockData,
  UnauthorizedError,
} from '@compass/common';
import { db, Enums, userDynamoDb } from '@compass/database';
import { Response } from 'express';
import { decode } from 'jsonwebtoken';

import config from '@/src/config';
import {
  authHelpers as sharedAuthHelpers,
  type GoogleOAuthBody,
  type LoginBody,
  type RegistrationBody,
  type RequestResetPasswordBody,
  type ResetPasswordBody,
} from '@/src/shared/auth';
import { asyncWrapper } from '@/src/shared/helpers';
import { TypedBodyRequest } from '@/src/types/express';

import authHelpers from './auth.helpers';
import htmlTemplates from './auth.html-templates';
import repository from './auth.repository';
import { getGoogleIdToken } from './oauth';

async function registration(req: TypedBodyRequest<RegistrationBody>, res: Response) {
  if (!config.ALLOW_REGISTRATION) throw new ForbiddenError('Registration not available.');

  const { name, email, companyRoleId, password, socialLoginProvider } = req.body.user;
  const { countryId, industryId } = req.body.company;

  const [existingUserInDb, existingUserInDynamoDb] = await Promise.all([
    repository.getUserIdByEmail(email),
    userDynamoDb.getUser(email),
  ]);
  if (existingUserInDb || existingUserInDynamoDb) throw new ConflictError('User already exists.');

  await authHelpers.validateRegistrationData(req.body);

  const country = await repository.getCountryCodeAndCurrencyById(countryId);
  if (!country) throw new ConflictError('Country not found.', { countryId });

  const { code, defaultCurrency: currency } = country;

  if (!authHelpers.checkIfCorrectRegion(code)) return await authHelpers.registerInDifferentRegion(res, req.body, code);

  const [{ trialExpiresAt, status, appRestrictedFeatures, createAppUsagesData }, categoriesOnIndustries] =
    await Promise.all([authHelpers.getAppUsageData(), repository.getCategoriesByIndustryId(industryId)]);

  const dateFormat = code === 'US' ? Enums.DateFormat.MM_DD_YYYY : Enums.DateFormat.DD_MM_YYYY;
  const decimalPoint = code === 'US' ? Enums.DecimalPoint.PERIOD : Enums.DecimalPoint.COMMA;

  const hashedPassword = password ? await sharedAuthHelpers.hashPassword(password) : null;
  const userData = { name, email, password: hashedPassword, status };
  const companyData = {
    ...req.body.company,
    trialExpiresAt,
    status,
    appRestrictedFeatures,
    dateFormat,
    decimalPoint,
    currency,
  };

  const createdUser = await db.$transaction(async (tx) => {
    const createdUser = await repository.createUser(
      userData,
      companyRoleId,
      socialLoginProvider,
      companyData,
      createAppUsagesData,
      tx,
    );

    const userId = createdUser.id;
    const { id: companyId, currency: companyCurrency } = createdUser.company;

    await repository.createCategories(companyId, categoriesOnIndustries, tx);
    await repository.createInitialWidgetRows({ companyId, currency: companyCurrency, userId, isAccrual: false }, tx);
    await repository.createInitialAlerts({ companyId, currency: companyCurrency, userId, isAccrual: false }, tx);
    await mockData.seed({ companyId, isSeed: false }, tx);

    await userDynamoDb.createUser({ email });

    return createdUser;
  });

  juneService.identify(createdUser.id, createdUser.email, createdUser.name);

  juneService.track.create(createdUser.id, 'User');

  logger.slack({ email: createdUser.email }, 'User registered');

  authHelpers.respondWithUser(res, createdUser, 201);
}

async function login(req: TypedBodyRequest<LoginBody>, res: Response) {
  const { email, password } = req.body;

  const unauthorizedErrorObject = ['Invalid credentials', req.body.email] as const;

  const existingUserInDb = await repository.getUserByEmail(email);

  if (!existingUserInDb) {
    const existingUserInDynamoDb = await userDynamoDb.getUser(email);
    if (!existingUserInDynamoDb) throw new UnauthorizedError(...unauthorizedErrorObject);

    return await authHelpers.loginInRegionWithPassword(res, req.body, existingUserInDynamoDb.region as AwsRegion);
  }

  const isCorrectPassword = await sharedAuthHelpers.verifyPassword(password, String(existingUserInDb.password));
  if (!isCorrectPassword) throw new UnauthorizedError(...unauthorizedErrorObject);

  juneService.identify(existingUserInDb.id, existingUserInDb.email, existingUserInDb.name);
  juneService.track.success(existingUserInDb.id, 'User logged in');

  authHelpers.respondWithUser(res, existingUserInDb);
}

// idToken is sent in body in internal cross-region compass-api requests because code can only be used once
async function googleOAuth(req: TypedBodyRequest<GoogleOAuthBody>, res: Response) {
  if (!req.isInternalRequest && req.body.idToken) throw new BadRequestError();

  const idToken = req.body.idToken ?? (await getGoogleIdToken(req.body.code));

  const decodedToken = decode(idToken);
  if (!decodedToken || typeof decodedToken === 'string') throw new InternalServerError('Token decode failed.');
  const { email: decodedEmail, given_name: name = '' } = decodedToken;
  const email = decodedEmail.toLowerCase();

  const existingSocialLogin = await repository.getSocialLogin(email, Enums.SocialLoginProvider.GOOGLE);

  /** Login user with existing social login */
  if (existingSocialLogin?.user) {
    juneService.identify(existingSocialLogin.user.id, existingSocialLogin.user.email, existingSocialLogin.user.name);
    juneService.track.success(existingSocialLogin.user.id, 'User logged in', 'Google');

    return authHelpers.respondWithUser(res, existingSocialLogin.user);
  }

  /** Login user with existing email/password account and create social login */
  const userWithPassword = await repository.getUserByEmail(email);
  if (userWithPassword) {
    await repository.createSocialLogin({
      userId: userWithPassword.id,
      provider: Enums.SocialLoginProvider.GOOGLE,
      credential: email,
    });

    juneService.identify(userWithPassword.id, userWithPassword.email, userWithPassword.name);
    juneService.track.success(userWithPassword.id, 'User logged in', 'Google');

    return authHelpers.respondWithUser(res, userWithPassword);
  }

  const userInDynamoDb = await userDynamoDb.getUser(email);
  if (userInDynamoDb) {
    return await authHelpers.loginInRegionWithSocialLogin(
      res,
      { ...req.body, idToken },
      userInDynamoDb.region as AwsRegion,
    );
  }

  if (!config.ALLOW_REGISTRATION) throw new ForbiddenError('Registration not available.');

  res.json(sharedAuthHelpers.createOAuthResponse(email, name));
}

async function requestResetPassword(req: TypedBodyRequest<RequestResetPasswordBody>, res: Response) {
  const userInDb = await repository.getUserByEmail(req.body.email);
  if (!userInDb) {
    const userInDynamoDb = await userDynamoDb.getUser(req.body.email);
    if (!userInDynamoDb) throw new ConflictError('User does not exist.', { email: req.body.email });

    return await authHelpers.requestResetPasswordInRegion(res, req.body, userInDynamoDb.region as AwsRegion);
  }

  const shortLivedToken = authHelpers.generateResetToken(userInDb.id);

  const resetUrl = `${config.WEB_APP_ORIGIN}/reset-password?token=${shortLivedToken}`;

  await awsCommon.sesService.sendEmail({
    toAddresses: [userInDb.email],
    subject: 'Compass AI - Forgotten password',
    body: htmlTemplates.constructResetPasswordEmail(resetUrl),
  });

  res.status(204).send();
}

async function resetPassword(req: TypedBodyRequest<ResetPasswordBody>, res: Response) {
  const { token, newPassword } = req.body;

  const { sub } = await authHelpers.verifyResetToken(token).catch(() => {
    throw new BadRequestError('Token is invalid.');
  });

  const user = await repository.updateUserById(Number(sub), {
    password: await sharedAuthHelpers.hashPassword(newPassword),
  });

  authHelpers.respondWithUser(res, user);
}

export default {
  registration: asyncWrapper(registration),
  login: asyncWrapper(login),
  googleOAuth: asyncWrapper(googleOAuth),
  requestResetPassword: asyncWrapper(requestResetPassword),
  resetPassword: asyncWrapper(resetPassword),
};
