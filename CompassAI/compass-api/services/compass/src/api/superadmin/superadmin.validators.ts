import { Enums } from '@compass/database';
import z from 'zod';

const genericNotificationValidator = z.object({
  companyId: z.number(),
  notificationTitle: z.string(),
  notificationDescription: z.string().optional(),
  notificationContent: z.string(),
  emailSubjectAndTitle: z.string(),
  emailContent: z.string(),
  priority: z.nativeEnum(Enums.Priority).optional(),
});
export type CreateGenericNotificationBody = z.infer<typeof genericNotificationValidator>;

const loginAsSomeoneValidator = z.object({
  email: z
    .string()
    .email()
    .transform((email) => email.toLowerCase()),
});
export type LoginAsSomeoneBody = z.infer<typeof loginAsSomeoneValidator>;

export default {
  genericNotificationValidator,
  loginAsSomeoneValidator,
};
