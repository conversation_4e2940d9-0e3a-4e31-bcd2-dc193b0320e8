import { Enums } from '@compass/database';
import z from 'zod';

import helpers from './auth.helpers';

const registrationValidator = z.object({
  user: z.object({
    name: z.string().min(3).max(100),
    email: z
      .string()
      .email()
      .transform((email) => email.toLowerCase()),
    companyRoleId: z.number().nullable().optional(),
    password: z.string().min(helpers.minPasswordLength).max(helpers.maxPasswordLength).optional(),
    socialLoginProvider: z.nativeEnum(Enums.SocialLoginProvider).optional(),
  }),
  company: z.object({
    name: z.string().min(1).max(50),
    address: z.string().min(1).max(100),
    countryId: z.number(),
    industryId: z.number(),
    typeId: z.number(),
  }),
});
export type RegistrationBody = z.infer<typeof registrationValidator>;

const loginValidator = z.object({
  email: z
    .string()
    .email()
    .transform((email) => email.toLowerCase()),
  password: z.string().min(1).max(helpers.maxPasswordLength),
});
export type LoginBody = z.infer<typeof loginValidator>;

const googleOAuthValidator = z.object({ code: z.string(), idToken: z.string().optional() });
export type GoogleOAuthBody = z.infer<typeof googleOAuthValidator>;

const requestResetPasswordValidator = z.object({
  email: z
    .string()
    .email()
    .transform((email) => email.toLowerCase()),
});
export type RequestResetPasswordBody = z.infer<typeof requestResetPasswordValidator>;

const resetPasswordValidator = z.object({
  token: z.string(),
  newPassword: z.string().min(helpers.minPasswordLength).max(helpers.maxPasswordLength),
});
export type ResetPasswordBody = z.infer<typeof resetPasswordValidator>;

export default {
  registrationValidator,
  loginValidator,
  googleOAuthValidator,
  requestResetPasswordValidator,
  resetPasswordValidator,
};
