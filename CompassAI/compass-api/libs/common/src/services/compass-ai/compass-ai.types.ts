import { Enums } from '@compass/database';
import type { Readable } from 'stream';

import type { ChangeInfoAction, Month, UpperEntriesType, YearMonth } from '../../types';

export type EnrichTransactionsInput = {
  transactions: Array<{
    accountName: string | null;
    description: string | null;
    transactionId: number;
    similarTransactions: Array<{
      accountName: string | null;
      description: string | null;
      merchantName: string;
    }>;
  }>;
  companyName: string;
};
export type EnrichTransactionsOutput = {
  data: {
    enriched_transactions: Array<{
      transactionId: number | null;
      merchantName: string | null;
      merchantType: 'private' | 'business' | string | null;
      transactionType: 'bankTransaction' | 'cardTransaction' | string | null;
      confidenceScore: number | null;
    }>;
  };
};

export type CategorizeTransactionsInput = {
  transactions: Array<{
    transactionId: number;
    merchantName: string;
    description: string;
    merchantType: Lowercase<Enums.MerchantType>;
    similarTransactions: Array<{
      merchantName: string | null;
      description: string | null;
      leafCategoryId: number | null;
    }>;
  }>;
  companyId: number;
  transactionsType: Lowercase<Enums.FinancialType>;
  companyName: string;
  country: string | null;
  industry: string | null;
};
export type CategorizeTransactionsOutput = {
  data: {
    categorized_transactions: Array<{
      transactionId: number | null;
      leafCategoryId: number | null;
      confidenceScore: number | null;
    }>;
  };
};

export type ProcessFinancialInsightInput = {
  companyId: number;
  industry: string;
  dateStart: string;
  dateEnd: string;
  totalCurrentIncome: string;
  totalCurrentExpense: string;
  totalPreviousIncome: string;
  totalPreviousExpense: string;
  chartData: Array<{ label: string; value: string; extra: Lowercase<Enums.FinancialType> }>;
  leafCategories: Array<{
    id: number | null;
    accounts: Array<{ name: string; totalAmount: string }>;
  }>;
};
export type ProcessFinancialInsightOutput = {
  data: {
    summary: Array<string>;
    suggestions: Array<{
      title: string;
      description: string;
      action: Enums.FinancialInsightAction;
      actionPrompt: string;
    }>;
  };
};

export type GetSimilarDescriptionsInput = {
  querySentence: string;
  passageSentences: Array<string>;
  passageThreshold: number;
};
export type GetSimilarDescriptionsOutput = {
  query: string;
  topSimilarities: Array<{ description: string; similarity: number }>;
};

export type BankAccountForAi = { id: number; name: string; currency: Enums.Currency };
export type LeafCategoryForAi = { id: number; accounts: Array<{ id: number; name: string }> };
export type AlertPromptInput = {
  companyId: number;
  prompt: string;
  currencies: Array<Enums.Currency>;
  defaultCurrency: Enums.Currency;
  hasTransactions: boolean;
  hasAccrual: boolean;
  leafCategories: Array<LeafCategoryForAi>;
  bankAccounts: Array<BankAccountForAi>;
  labels: Array<string>;
  groups: Array<string>;
  error_context?: string | null;
};
export type AlertPromptOutput = {
  prompt: string;
  data: {
    reasoning: string;
    evaluationFrequency?: string | null;
    evaluationDay: Array<string>;
    sqlQuery: string;
    displayQueries: Array<{ displayQuerySql: string; displayQueryTitle: string; displayQueryType: string }>;
    alertGroupName: string;
    title: string;
    notification: {
      notificationMessage: string;
      notificationQuery: string;
    };
    instructions: Array<string>;
  };
};

type WidgetChartAxis = {
  name: string;
  value: string;
  format: string;
  currency?: string | null;
  seriesKey?: string | null;
};
export type WidgetChartChatInput = {
  sessionId?: string | null;
  companyId: number;
  prompt: string;
  currencies: Array<Enums.Currency>;
  defaultCurrency: Enums.Currency;
  hasTransactions: boolean;
  hasAccrual: boolean;
  leafCategories: Array<LeafCategoryForAi>;
  bankAccounts: Array<BankAccountForAi>;
  labels: Array<string>;
  error_context?: string | null;
};
export type WidgetChartChatOutput = {
  prompt: string;
  sessionId: string;
  data: {
    reasoning: string;
    composition: string;
    charts: Array<{
      visualType: string;
      xAxis: WidgetChartAxis;
      yAxis: WidgetChartAxis;
      sqlQuery: string;
      legendTitle: string;
      displayDetails: {
        type: string;
        sqlTemplate: string;
        valueColumn: Array<{ key: string; columnName: string }>;
        title: string;
      };
    }>;
    title: string;
    description: string;
  };
};

export type WidgetPromptSuggestionsInput = {
  companyId: number;
  industry: string;
  entryDateStart: string;
  leafCategories: Array<LeafCategoryForAi>;
  hasTransactions: boolean;
  hasAccrual: boolean;
};
export type AiAssistantPromptSuggestionsInput = {
  companyId: number;
  entryDateStart: string;
  leafCategories: Array<LeafCategoryForAi>;
  hasTransactions: boolean;
  hasAccrual: boolean;
};
export type PromptSuggestionsOutput = {
  data: {
    suggestions: Array<string>;
  };
};

type WidgetInsightsItem = {
  title: string;
  description: string;
  action: string;
  actionPrompt?: string | null;
};
export type WidgetInsightsInput = {
  title: string;
  industry: string;
  hasTransactions: boolean;
  hasAccrual: boolean;
  data: Array<{
    label: string;
    value: string;
    extra?: string | null;
  }>;
};
export type WidgetInsightsOutput = {
  data: {
    strengths: Array<WidgetInsightsItem>;
    concerns: Array<WidgetInsightsItem>;
    suggestions: Array<WidgetInsightsItem>;
  };
};

export type CategorizeInvoicesBillsInput = {
  invoices: Array<{
    invoiceId: number;
    accountName: string;
    lineDescriptions: Array<string>;
    similarInvoices: Array<{
      accountName: string;
      lineDescriptions: Array<string>;
      leafCategoryId: number | null;
    }>;
  }>;
  companyId: number;
  invoiceType: Lowercase<Enums.FinancialType>;
  companyName: string | null;
  country: string | null;
  industry: string | null;
};
export type CategorizeInvoicesBillsOutput = {
  data: {
    categorized_invoices: Array<{
      invoiceId: number;
      leafCategoryId: number | null;
      confidenceScore: number | null;
    }>;
  };
};

export type LinkTransactionsInput = {
  invoices: Array<{
    invoiceId: number;
    docNumber: string | null;
    accountName: string | null;
    issueDate: string | null;
    dueDate: string | null;
    amount: string;
    currency: Enums.Currency;
    invoiceDescription: string | null;
    potentialLinkTransactions: Array<{
      transactionId: number;
      accountName: string | null;
      description: string | null;
      date: string;
      paidAmount: string;
      paidCurrency: Enums.Currency;
      convertedAmount: string;
      convertedCurrency: Enums.Currency | null;
    }>;
  }>;
  companyName: string | null;
  country: string | null;
  industry: string | null;
};
export type LinkTransactionsOutput = {
  data: {
    linked_invoices: Array<{
      invoiceId: number | null;
      transactionId: number | null;
      confidenceScore: number | null;
    }>;
  };
};

export type LinkInvoicesBillsInput = {
  transactions: Array<{
    transactionId: number;
    accountName: string | null;
    description: string | null;
    date: string;
    paidAmount: string;
    paidCurrency: Enums.Currency;
    potentialLinkInvoices: Array<{
      invoiceId: number;
      docNumber: string | null;
      accountName: string | null;
      issueDate: string | null;
      dueDate: string | null;
      amount: string;
      currency: Enums.Currency;
      invoiceDescription: string | null;
    }>;
  }>;
  companyName: string | null;
  country: string | null;
  industry: string | null;
};
export type LinkInvoicesBillsOutput = {
  data: {
    linked_transactions: Array<{
      invoiceId: number | null;
      transactionId: number | null;
      confidenceScore: number | null;
    }>;
  };
};

export type ReadInvoiceBillDocumentOutput = {
  data: {
    reasoning: string | null;
    isCompanySender: boolean | null;
    docNumber: string | null;
    currency: Enums.Currency;
    totalAmount: number;
    amountWithoutTax: number | null;
    taxPercentage: number | null;
    totalTaxAmount: number;
    issueDate: string | null;
    netPaymentTerm: number | null;
    dueDate: string | null;
    senderName: string | null;
    senderInfo: string | null;
    receiverName: string | null;
    receiverInfo: string | null;
    lines: Array<{
      amount: number;
      description: string | null;
      unitPrice: number | null;
      quantity: number | null;
      tax: number | null;
      extra: string | null;
    }>;
    extra: string | null;
  };
};

type ChangeInfo = { reason: string; action: ChangeInfoAction };

export type ContextForecastCompany = {
  id: number;
  name: string;
  country: string;
  industry: string;
  defaultCurrency: string;
};

export type ContextForecastAccount = {
  id: number;
  name: string;
  type: string;
  info: string | null;
};

export type PatternScope = {
  scopeType: Enums.PatternScopeType;
  leafCategoryIds?: Array<number> | null;
  description: string;
};

type HistoricalMonthlyData = {
  period: YearMonth;
  financialActivities: Array<{
    type: UpperEntriesType;
    amount: number;
    description: string | null;
    leafCategoryId: number | null;
    issueDate: Date | null;
    dueDate: Date | null;
    paidDate: Date | null;
  }>;
};

export type AccountContextInput = {
  company: ContextForecastCompany;
  account: ContextForecastAccount;
  historicalMonthlyData: Array<HistoricalMonthlyData>;
};
export type AccountContextOutput = {
  data: {
    reasoning: string;
    recurringPatterns: Array<{
      description: string;
      scope: {
        scopeType: Enums.PatternScopeType;
        description: string;
        leafCategoryIds?: Array<number> | null | undefined;
      };
      frequency: Enums.RecurringFrequency;
      typicalAmount: number;
      firstObserved: string;
      lastObserved: string;
      occurrenceCount: number;
      id?: number | null | undefined;
      typicalDayOfMonth?: number | null | undefined;
      dayVariance?: number | null | undefined;
      amountVariance?: number | null | undefined;
    }>;
    seasonalPatterns: Array<{
      description: string;
      scope: {
        scopeType: Enums.PatternScopeType;
        description: string;
        leafCategoryIds: Array<number> | null;
      };
      peakPeriods: Array<Month>;
      troughPeriods: Array<Month>;
      typicalAmount: number;
      cyclesObserved: number;
      id: number | null;
      amountVariance: number | null;
      peakMultiplier: number | null;
    }>;
    trends: Array<{
      description: string;
      scope: {
        scopeType: Enums.PatternScopeType;
        description: string;
        leafCategoryIds: Array<number> | null;
      };
      trendType: Enums.TrendType;
      trendStartPeriod: string;
      lastActivePeriod: string;
      id: number | null;
      volatility: number | null;
    }>;
    accountSummary: {
      reasoning: string;
      accountStatus: Enums.AccountStatus;
      primaryDrivers: Array<string>;
      keyRisks: Array<string>;
      lastActivityDate: string;
      summary: string;
    };
  };
};

export type AccountContextUpdateInput = {
  company: ContextForecastCompany;
  account: ContextForecastAccount;
  newMonthData: {
    period: YearMonth;
    totalAmount: number;
    leafCategoryAmounts: Array<{
      leafCategoryId: number;
      amount: number;
      financialActivities: Array<{
        type: UpperEntriesType;
        amount: number;
        description: string | null;
        issueDate: Date | null;
        dueDate: Date | null;
        transactionDate: Date | null;
      }>;
    }>;
  };
  previousContext: {
    historicalPeriodContext: Array<{
      id: number;
      period: YearMonth;
      summary: string;
      attentionLevel: Enums.AttentionLevel;
    }>;
    historicalRecurringPatterns: Array<{
      id: number;
      leafCategoryId: number;
      notes: string;
      amount: number;
      frequency: Enums.RecurringFrequency;
      typicalDayOfMonth: number | null;
    }>;
    historicalSeasonalities: Array<{
      id: number;
      leafCategoryId: number;
      notes: string;
      peakPeriods: Array<YearMonth> | null;
      troughPeriods: Array<YearMonth> | null;
    }>;
    historicalTrends: Array<{
      id: number;
      leafCategoryId: number;
      notes: string;
      trendType: Enums.TrendType;
      growthRate: number | null;
      lastActivePeriod: YearMonth | null;
    }>;
    historicalHumanResponse: Array<{
      id: number;
      period: YearMonth;
      humanInsightQuery: string | null;
      humanInsightAnswer: string | null;
    }>;
  };
};
export type AccountContextUpdateOutput = {
  data: {
    reason: string;
    changedPeriodContext: {
      id: number;
      period: YearMonth;
      summary: string;
      attentionLevel: Enums.AttentionLevel;
      changeInfo: ChangeInfo;
    };
    changedRecurringPatterns: Array<{
      id: number;
      leafCategoryId: number;
      notes: string;
      amount: number;
      frequency: Enums.RecurringFrequency;
      typicalDayOfMonth: number | null;
      changeInfo: ChangeInfo;
    }>;
    changedSeasonalities: Array<{
      id: number;
      leafCategoryId: number;
      notes: string;
      peakPeriods: Array<YearMonth> | null;
      troughPeriods: Array<YearMonth> | null;
      changeInfo: ChangeInfo;
    }>;
    changedTrends: Array<{
      id: number;
      leafCategoryId: number;
      notes: string;
      trendType: Enums.TrendType;
      growthRate: number | null;
      lastActivePeriod: YearMonth | null;
      changeInfo: ChangeInfo;
    }>;
  };
};

export type ForecastPatternScope = {
  scopeType: Enums.PatternScopeType;
  leafCategoryIds?: Array<number> | null;
  description: string;
};

export type AccountForecastInput = {
  company: ContextForecastCompany;
  account: ContextForecastAccount;
  historicalMonthlyData: Array<HistoricalMonthlyData>;
  accountSummary: {
    accountStatus: Enums.AccountStatus;
    primaryDrivers: Array<string>;
    keyRisks: Array<string>;
    lastActivityDate: string;
    summary: string;
  };
  recurringPatterns: Array<{
    scope: ForecastPatternScope;
    description: string;
    frequency: Enums.RecurringFrequency;
    typicalDayOfMonth?: number | null;
    dayVariance?: number | null;
    typicalAmount: number;
    amountVariance?: number | null;
    firstObserved: string;
    lastObserved: string;
    occurrenceCount: number;
  }>;
  seasonalPatterns: Array<{
    scope: ForecastPatternScope;
    description: string;
    peakPeriods: Array<Month>;
    troughPeriods: Array<Month>;
    typicalAmount: number;
    amountVariance?: number | null;
    peakMultiplier?: number | null;
    cyclesObserved: number;
  }>;
  trends: Array<{
    scope: ForecastPatternScope;
    description: string;
    trendType: Enums.TrendType;
    volatility?: number | null;
    trendStartPeriod: YearMonth;
    lastActivePeriod: YearMonth;
  }>;
  startPeriod: YearMonth;
  endPeriod: YearMonth;
};
export type AccountForecastOutput = {
  data: {
    reasoning: string;
    keyRisks: Array<string>;
    majorAssumptions: Array<string>;
    forecasts: Array<{
      reasoning: string;
      period: string;
      forecastedTransactions: Array<{
        description: string;
        amount: number;
        estimatedDate: string;
        leafCategoryId: number | null;
      }>;
    }>;
  };
};

export type CreateInitialWorkflowNodesInput = {
  company: ContextForecastCompany;
  forecast_id: number;
  current_node_id: null;
};
export type CreateInitialWorkflowNodesOutput = { nodes_created: { node_ids: Array<number> } };

export type ForecastWorkflowStreamInput = {
  company: ContextForecastCompany;
  forecast_id: number;
  user_input: string;
  current_node_id: number | null;
};
export type ForecastWorkflowStreamOutput = Readable;

export type GetForecastWorkflowNodesInput = { companyId: number; forecastId: number };
export type GetForecastWorkflowNodesOutput = {
  nodes: Array<{ id: number; name: string; financialType: Enums.FinancialType }>;
};

export type GetForecastWorkflowNodeInput = { nodeId: number };
export type GetForecastWorkflowNodeOutput = {
  id: number;
  name: string;
  financial_type: Enums.FinancialType;
  conversation_history: Array<{ timestamp: Date; content: string; source: 'SYSTEM' | 'AI' }>;
  preview_data: Array<{
    period: YearMonth;
    account_id: number | null;
    account: string;
    amounts: Record<string, number>; // leafCategoryId -> amount
  }>;
};

export type RemoveForecastNodeInput = { company_id: number; forecast_id: number; node_id: number };

export type AICfoOnboardingStreamInput = {
  company: ContextForecastCompany;
  answers?: Array<string>;
  step: string;
  user_name: string;
  user_role: string;
  intro_interaction: boolean;
};
export type AICfoOnboardingStreamOutput = Readable;

export type AiAssistantChatStreamInput = {
  sessionId?: string | null;
  prompt: string;
  companyId: number;
  companyName?: string | null;
  country?: string | null;
  industry?: string | null;
  currencies: Array<Enums.Currency>;
  defaultCurrency: Enums.Currency;
  hasTransactions: boolean;
  hasAccrual: boolean;
  leafCategories: Array<LeafCategoryForAi>;
  bankAccounts: Array<BankAccountForAi>;
  labels: Array<string>;
  alertGroups: Array<string>;
};
export type AiAssistantChatStreamOutput = Readable;

export type GetAiAssistantChatHistoryInput = { companyId: number; sessionId: string };
export type GetAiAssistantChatHistoryOutput = Array<
  { role: 'user'; content: string } | { role: 'assistant'; content: { components: Array<any> } } // components validated in handler
>;

export type DeleteAiAssistantChatHistoryInput = { companyId: number; sessionId: string };
export type DeleteAiAssistantChatHistoryOutput = { success: boolean };
