import axios, { type AxiosError } from 'axios';
import axiosRetry from 'axios-retry';
import FormData from 'form-data';

import { backoffHelpers, logExternalApiError } from '../../helpers';
import logger from '../../logger';
import type { FileOptions } from '../../types';
import type {
  AccountContextInput,
  AccountContextOutput,
  AccountContextUpdateInput,
  AccountContextUpdateOutput,
  AccountForecastInput,
  AccountForecastOutput,
  AiAssistantChatStreamInput,
  AiAssistantChatStreamOutput,
  AiAssistantPromptSuggestionsInput,
  AICfoOnboardingStreamInput,
  AICfoOnboardingStreamOutput,
  AlertPromptInput,
  AlertPromptOutput,
  CategorizeInvoicesBillsInput,
  CategorizeInvoicesBillsOutput,
  CategorizeTransactionsInput,
  CategorizeTransactionsOutput,
  CreateInitialWorkflowNodesInput,
  CreateInitialWorkflowNodesOutput,
  DeleteAiAssistantChatHistoryInput,
  DeleteAiAssistantChatHistoryOutput,
  EnrichTransactionsInput,
  EnrichTransactionsOutput,
  ForecastWorkflowStreamInput,
  ForecastWorkflowStreamOutput,
  GetAiAssistantChatHistoryInput,
  GetAiAssistantChatHistoryOutput,
  GetForecastWorkflowNodeInput,
  GetForecastWorkflowNodeOutput,
  GetForecastWorkflowNodesInput,
  GetForecastWorkflowNodesOutput,
  GetSimilarDescriptionsInput,
  GetSimilarDescriptionsOutput,
  LinkInvoicesBillsInput,
  LinkInvoicesBillsOutput,
  LinkTransactionsInput,
  LinkTransactionsOutput,
  ProcessFinancialInsightInput,
  ProcessFinancialInsightOutput,
  PromptSuggestionsOutput,
  ReadInvoiceBillDocumentOutput,
  RemoveForecastNodeInput,
  WidgetChartChatInput,
  WidgetChartChatOutput,
  WidgetInsightsInput,
  WidgetInsightsOutput,
  WidgetPromptSuggestionsInput,
} from './compass-ai.types';

const compassAiApi = axios.create({ baseURL: process.env.COMPASS_AI_URL });

axiosRetry(compassAiApi, {
  retries: 3,
  retryDelay: (attemptsMade) => backoffHelpers.backoffStrategy(attemptsMade),
  retryCondition: (error: AxiosError) => {
    logger.warn(error, 'Retrying Compass AI request');

    if (!error.response || error.response.status >= 500) return true;
    if (axiosRetry.isNetworkError(error)) return true;

    return false;
  },
});

async function enrichTransactions(
  data: EnrichTransactionsInput,
  shouldRetryLater: boolean,
): Promise<EnrichTransactionsOutput['data']['enriched_transactions'] | { shouldRetryLater: true }> {
  try {
    const response = await compassAiApi.post<EnrichTransactionsOutput>('/transactions/enrichment', data);
    return response.data.data.enriched_transactions;
  } catch (error) {
    logExternalApiError('Compass AI enrich transactions error', error, data);

    if (shouldRetryLater) return { shouldRetryLater: true };

    return data.transactions.map(() => ({
      transactionId: null,
      merchantName: null,
      merchantType: null,
      transactionType: null,
      confidenceScore: null,
    }));
  }
}

async function categorizeTransactions(
  data: CategorizeTransactionsInput,
  shouldRetryLater: boolean,
): Promise<CategorizeTransactionsOutput['data']['categorized_transactions'] | { shouldRetryLater: true }> {
  try {
    const response = await compassAiApi.post<CategorizeTransactionsOutput>('/transactions/categorization', data);
    return response.data.data.categorized_transactions;
  } catch (error) {
    logExternalApiError('Compass AI categorize transactions error', error, data);

    if (shouldRetryLater) return { shouldRetryLater: true };

    return data.transactions.map(() => ({
      transactionId: null,
      leafCategoryId: null,
      confidenceScore: null,
    }));
  }
}

async function processFinancialInsight(
  data: ProcessFinancialInsightInput,
): Promise<ProcessFinancialInsightOutput['data']> {
  try {
    const response = await compassAiApi.post<ProcessFinancialInsightOutput>('/insights/financial-summary', data);
    return response.data.data;
  } catch (error) {
    logExternalApiError('Compass AI process financial insights error', error, data);
    throw error;
  }
}

async function getSimilarDescriptions(data: GetSimilarDescriptionsInput): Promise<GetSimilarDescriptionsOutput> {
  try {
    const response = await compassAiApi.post<GetSimilarDescriptionsOutput>('/description-similarity-score', data);
    return response.data;
  } catch (error) {
    logExternalApiError('Compass AI get similar description error', error, data);

    return { query: data.querySentence, topSimilarities: [] };
  }
}

async function alertPrompt(data: AlertPromptInput): Promise<AlertPromptOutput['data']> {
  try {
    const response = await compassAiApi.post<AlertPromptOutput>('/alert-prompt', data);
    return response.data.data;
  } catch (error) {
    logExternalApiError('Compass AI alert prompt error', error, data);

    throw error;
  }
}

async function widgetChartChat(data: WidgetChartChatInput): Promise<Omit<WidgetChartChatOutput, 'prompt'>> {
  try {
    const response = await compassAiApi.post<WidgetChartChatOutput>('/widget/chart-chat', data);
    return { data: response.data.data, sessionId: response.data.sessionId };
  } catch (error) {
    logExternalApiError('Compass AI widget chart data error', error, data);

    throw error;
  }
}

async function widgetPromptSuggestions(
  data: WidgetPromptSuggestionsInput,
): Promise<PromptSuggestionsOutput['data']['suggestions']> {
  try {
    const response = await compassAiApi.post<PromptSuggestionsOutput>('/widget/prompt-suggestions', data);
    return response.data.data.suggestions;
  } catch (error) {
    logExternalApiError('Compass AI widget prompt suggestions error', error, data);

    throw error;
  }
}

async function aiAssistantPromptSuggestions(
  data: AiAssistantPromptSuggestionsInput,
): Promise<PromptSuggestionsOutput['data']['suggestions']> {
  try {
    const response = await compassAiApi.post<PromptSuggestionsOutput>('/assistant/prompt-suggestions', data);
    return response.data.data.suggestions;
  } catch (error) {
    logExternalApiError('Compass AI ai assistant prompt suggestions error', error, data);

    throw error;
  }
}

async function widgetInsights(data: WidgetInsightsInput): Promise<WidgetInsightsOutput['data']> {
  try {
    const response = await compassAiApi.post<WidgetInsightsOutput>('/insights/widget-analysis', data);
    return response.data.data;
  } catch (error) {
    logExternalApiError('Compass AI widget insights error', error, data);

    throw error;
  }
}

async function categorizeInvoicesBills(
  data: CategorizeInvoicesBillsInput,
): Promise<CategorizeInvoicesBillsOutput['data']['categorized_invoices']> {
  try {
    const response = await compassAiApi.post<CategorizeInvoicesBillsOutput>('/invoices/categorization', data);
    return response.data.data.categorized_invoices;
  } catch (error) {
    logExternalApiError('Compass AI categorize invoices error', error, data);

    return data.invoices.map((_invoice, index) => ({ invoiceId: index, leafCategoryId: null, confidenceScore: null }));
  }
}

async function linkTransactions(
  data: LinkTransactionsInput,
): Promise<LinkTransactionsOutput['data']['linked_invoices']> {
  try {
    const response = await compassAiApi.post<LinkTransactionsOutput>('/invoices/link-transactions', data);
    return response.data.data.linked_invoices;
  } catch (error) {
    logExternalApiError('Compass AI link transactions error', error, data);

    return data.invoices.map((_invoice) => ({ invoiceId: null, transactionId: null, confidenceScore: null }));
  }
}

async function linkInvoicesBills(
  data: LinkInvoicesBillsInput,
): Promise<LinkInvoicesBillsOutput['data']['linked_transactions']> {
  try {
    const response = await compassAiApi.post<LinkInvoicesBillsOutput>('/transactions/link-invoices', data);
    return response.data.data.linked_transactions;
  } catch (error) {
    logExternalApiError('Compass AI link invoices/bills error', error, data);

    return data.transactions.map((_transaction) => ({ invoiceId: null, transactionId: null, confidenceScore: null }));
  }
}

async function readInvoiceBillDocument(
  companyName: string,
  isCompanySender: boolean,
  buffer: Buffer,
  options: FileOptions,
): Promise<ReadInvoiceBillDocumentOutput['data'] | null> {
  try {
    const formData = new FormData();
    formData.append('companyName', companyName);
    formData.append('isCompanySender', String(isCompanySender));
    formData.append('file', buffer, options);

    const response = await compassAiApi.post<ReadInvoiceBillDocumentOutput>('/invoices/read-document', formData);
    return response.data.data;
  } catch (error) {
    logExternalApiError('Compass AI read invoice/bill document error', error);

    return null;
  }
}

async function getAccountContext(data: AccountContextInput): Promise<AccountContextOutput['data']> {
  try {
    const response = await compassAiApi.post('/contexts/account', data);

    return response.data.data;
  } catch (error) {
    logExternalApiError('Compass AI Account context error', error, data);

    throw error;
  }
}

async function getAccountContextUpdate(data: AccountContextUpdateInput) {
  try {
    const response = await compassAiApi.post<AccountContextUpdateOutput>('/contexts/account/update', data);
    return response.data.data;
  } catch (error) {
    logExternalApiError('Compass AI Account context update error', error, data);

    throw error;
  }
}

async function getAccountForecast(data: AccountForecastInput): Promise<AccountForecastOutput['data']> {
  try {
    const response = await compassAiApi.post('/forecasts/account', data);

    return response.data.data;
  } catch (error) {
    logExternalApiError('Compass AI Account forecast error', error, data);

    throw error;
  }
}

async function createInitialWorkflowNodes(
  data: CreateInitialWorkflowNodesInput,
): Promise<CreateInitialWorkflowNodesOutput> {
  try {
    const response = await compassAiApi.post('/scenarios/initial', data);

    return response.data;
  } catch (error) {
    logExternalApiError('Compass AI initial workflow nodes error', error, data);

    throw error;
  }
}

async function getForecastWorkflowStream(data: ForecastWorkflowStreamInput): Promise<ForecastWorkflowStreamOutput> {
  try {
    const response = await compassAiApi.post('/scenarios/workflow/stream', data, { responseType: 'stream' });

    return response.data;
  } catch (error) {
    logExternalApiError('Compass AI forecast workflow chat stream initialization error', error, data);

    throw error;
  }
}

async function getForecastWorkflowNodes(data: GetForecastWorkflowNodesInput): Promise<GetForecastWorkflowNodesOutput> {
  try {
    const { companyId, forecastId } = data;

    const response = await compassAiApi.get(`/scenarios/nodes/${companyId}/${forecastId}`);

    return response.data;
  } catch (error) {
    logExternalApiError('Compass AI get workflow nodes error', error, data);

    throw error;
  }
}

async function getForecastWorkflowNode(data: GetForecastWorkflowNodeInput): Promise<GetForecastWorkflowNodeOutput> {
  try {
    const response = await compassAiApi.get(`/scenarios/node/${data.nodeId}`);

    return response.data;
  } catch (error) {
    logExternalApiError('Compass AI get workflow node error', error, data);

    throw error;
  }
}

async function removeForecastNode(data: RemoveForecastNodeInput): Promise<void> {
  try {
    await compassAiApi.post('/scenarios/remove-node', data);
  } catch (error) {
    logExternalApiError('Compass AI remove forecast node error', error, data);

    throw error;
  }
}

async function getAICfoOnboardingStream(data: AICfoOnboardingStreamInput): Promise<AICfoOnboardingStreamOutput> {
  try {
    const response = await compassAiApi.post('/cfo/onboarding/workflow/stream', data, { responseType: 'stream' });
    return response.data;
  } catch (error) {
    logExternalApiError('Compass AI CFO onboarding modal stream initialization error', error, data);

    throw error;
  }
}

async function aiAssistantChatStream(data: AiAssistantChatStreamInput): Promise<AiAssistantChatStreamOutput> {
  try {
    const response = await compassAiApi.post<AiAssistantChatStreamOutput>('/assistant/chat/stream', data, {
      responseType: 'stream',
    });
    return response.data;
  } catch (error) {
    logExternalApiError('Compass AI assistant chat stream initialization error', error, data);

    throw error;
  }
}

async function getAiAssistantChatHistory(
  data: GetAiAssistantChatHistoryInput,
): Promise<GetAiAssistantChatHistoryOutput> {
  try {
    const response = await compassAiApi.get<GetAiAssistantChatHistoryOutput>('/assistant/chat/history', {
      params: data,
    });
    return response.data;
  } catch (error) {
    logExternalApiError('Compass AI get assistant chat history error', error, data);

    throw error;
  }
}

async function deleteAiAssistantChatHistory(
  data: DeleteAiAssistantChatHistoryInput,
): Promise<DeleteAiAssistantChatHistoryOutput> {
  try {
    const response = await compassAiApi.delete<DeleteAiAssistantChatHistoryOutput>('/assistant/chat/history', {
      params: data,
    });
    return response.data;
  } catch (error) {
    logExternalApiError('Compass AI delete assistant chat history error', error, data);

    return { success: false };
  }
}

export default {
  enrichTransactions,
  categorizeTransactions,
  processFinancialInsight,
  getSimilarDescriptions,
  alertPrompt,
  widgetChartChat,
  widgetPromptSuggestions,
  aiAssistantPromptSuggestions,
  widgetInsights,
  categorizeInvoicesBills,
  linkTransactions,
  linkInvoicesBills,
  readInvoiceBillDocument,
  getAccountContext,
  getAccountContextUpdate,
  getAccountForecast,
  getForecastWorkflowStream,
  createInitialWorkflowNodes,
  getForecastWorkflowNodes,
  getForecastWorkflowNode,
  removeForecastNode,
  aiAssistantChatStream,
  getAiAssistantChatHistory,
  deleteAiAssistantChatHistory,
};
